<template>
  <a-select
    class="w-full"
    v-model:value="value"
    show-search
    v-bind="$attrs"
    :default-active-first-option="false"
    :show-arrow="false"
    :filter-option="false"
    :not-found-content="null"
    :options="data"
    :field-names="{
      label: 'name',
      value: 'name',
    }"
    size="large"
    @search="handleSearch"
    @change="handleChange"
  >
    <template #notFoundContent v-if="loading">
      <a-spin size="small"></a-spin>
    </template>
  </a-select>
</template>

<script setup>
const emits = defineEmits(['change'])
const value = defineModel({ default: '' })
const data = ref([])

let lastFetchId = 0

const loading = ref(false)

const handleSearch = useDebounceFn(async (val) => {
  if (!val) return
  lastFetchId += 1
  const fetchId = lastFetchId
  loading.value = true
  const res = await $fetch('/bussiness', {
    method: 'post',
    body: { word: val },
  })
  loading.value = false
  if (fetchId != lastFetchId) return
  data.value = (res.result?.items ?? []).filter((item) => has(item.creditCode) && item.regStatus.includes('存续'))
}, 500)

const handleChange = (_val, option) => {
  emits('change', option)
}
</script>
