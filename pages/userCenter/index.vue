<template>
  <NuxtLayout name="child-page" title="个人中心">
    <div w-full max-w-150 px-1>
      <!-- 用户头像和基本信息 -->
      <div flex flex-col items-center mb-8>
        <a-avatar :size="96" :src="user.pic">
          {{ user.nickName }}
        </a-avatar>
        <div my-2>
          <span font-size-24px text-white>{{ user.nickName }}</span>
        </div>
        <div v-if="company.shopCompanyId">
          <span color-gray>{{ company.merchantName }}</span>
        </div>
      </div>

      <!-- 个人设置区域 -->
      <div mb-6>
        <div mb-3 px-2>
          <span color-gray text-sm font-medium>个人设置</span>
        </div>
        <NuxtLink v-for="(item, index) in personalMenus" :key="index" :to="item.link">
          <div menu-item :class="{ 'border-b border-b-[#353541]': index < personalMenus.length - 1 }">
            <div flex items-center>
              <span :class="item.icon" mr-2></span>
              <span>{{ item.name }}</span>
            </div>
            <span class="i-a-right-outlined"></span>
          </div>
        </NuxtLink>
      </div>

      <!-- 个人空间 -->
      <div mb-6>
        <div mb-3 px-2>
          <span color-gray text-sm font-medium>个人空间</span>
        </div>
        <div menu-item @click="onSwitchCompany({ shopCompanyId: 0, merchantName: '个人空间' })">
          <div w-full flex items-center justify-between>
            <div flex items-center>
              <div font-size-20px rounded-full mr-2>
                <a-avatar :src="user.pic">{{ user.nickName }}</a-avatar>
              </div>
              <span>{{ user.nickName }}</span>
            </div>
            <span v-if="company.shopCompanyId == 0" color-primary font-size-5 class="i-a-check-outlined"></span>
          </div>
        </div>
      </div>

      <!-- 企业空间区域 -->
      <div mb-6>
        <div mb-3 px-2>
          <span color-gray text-sm font-medium>企业空间</span>
        </div>

        <!-- Loading状态 -->
        <div v-if="companyListLoading" menu-item>
          <div flex items-center justify-center py-4>
            <a-spin size="small" />
            <span ml-2 color-gray>加载企业列表中...</span>
          </div>
        </div>

        <!-- 有企业时显示列表 -->
        <div
          v-else-if="companyList.length > 0"
          v-for="(item, index) in companyList"
          :key="index"
          menu-item
          :class="{
            'border-b border-b-[#353541]': index < companyList.length - 1,
            'bg-[#2a2a32]': item.shopCompanyId === curCompanyId,
          }"
          @click="onSwitchCompany(item)"
        >
          <div w-full flex items-center justify-between>
            <div flex items-center>
              <div font-size-20px rounded-full mr-2>
                <a-avatar :src="item.merchantLogo">{{ item.merchantShortName || item.merchantName }}</a-avatar>
              </div>
              <div flex items-center>
                <div class="mr-4">{{ item.merchantShortName || item.merchantName }}</div>
                <div flex gap-1>
                  <a-tag v-for="tip in getCompanyTip(item)" :key="tip" size="small">{{ tip }}</a-tag>
                </div>
              </div>
            </div>
            <span
              v-if="item.shopCompanyId === curCompanyId"
              color-primary
              font-size-5
              class="i-a-check-outlined"
            ></span>
          </div>
        </div>

        <!-- 无企业时的空状态 -->
        <div v-else-if="!companyListLoading" menu-item>
          <div flex flex-col items-center justify-center py-6 color-gray>
            <span class="i-a-team-outlined" font-size-8 mb-2 opacity-60></span>
            <span text-sm>暂无企业空间</span>
            <span text-xs opacity-80 mt-1>您可以创建新企业或加入现有企业</span>
          </div>
        </div>
      </div>

      <!-- 企业管理区域 -->
      <div v-if="company.shopCompanyId">
        <NuxtLink
          v-for="(item, index) in enterpriseMenus"
          :key="index"
          :to="item.link"
          @click="handleEnterpriseClick(item)"
        >
          <div menu-item class="border-b border-b-[#353541]">
            <div flex items-center>
              <span :class="item.icon" mr-2></span>
              <span>{{ item.name }}</span>
            </div>
            <span class="i-a-right-outlined"></span>
          </div>
        </NuxtLink>

        <!-- 创建新企业 -->
        <div @click="showCreateEnterpriseModal">
          <div menu-item class="border-b border-b-[#353541]">
            <div flex items-center>
              <span class="i-a-plus-outlined" mr-2></span>
              <span>创建新企业</span>
            </div>
            <span class="i-a-right-outlined"></span>
          </div>
        </div>

        <!-- 加入企业 -->
        <div @click="auth('/enterpriseCenter/join')">
          <div menu-item>
            <div flex items-center>
              <span class="i-a-team-outlined" mr-2></span>
              <span>加入企业</span>
            </div>
            <span class="i-a-right-outlined"></span>
          </div>
        </div>
      </div>

      <!-- 如果没有企业，显示企业管理选项 -->
      <div v-else>
        <div mb-3 px-2>
          <span color-gray text-sm font-medium>企业管理</span>
        </div>
        <div @click="auth('/enterpriseCenter/join')">
          <div menu-item class="border-b border-b-[#353541]">
            <div flex items-center>
              <span class="i-a-team-outlined" mr-2></span>
              <span>加入企业</span>
            </div>
            <span class="i-a-right-outlined"></span>
          </div>
        </div>

        <div @click="showCreateEnterpriseModal">
          <div menu-item>
            <div flex items-center>
              <span class="i-a-plus-outlined" mr-2></span>
              <span>创建新企业</span>
            </div>
            <span class="i-a-right-outlined"></span>
          </div>
        </div>
      </div>
    </div>

    <CreateEnterprise ref="createEnterpriseRef" />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import { getCompany } from '~/api/mall-manage'
import { getShopsByCompanyId, isEnterpriseAdmin } from '~/api/mall-platform'
import { getMerchantByUser } from '~/api/mall-manage/index'

const route = useRoute()
const router = useRouter()
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)
const { setCompany } = companyStoreObj

// Company type definition
type CompanyItem = {
  shopCompanyId: number
  merchantLogo?: string
  merchantShortName?: string
  merchantName: string
  type: number
  status?: number
  creditCode?: string
}

// Create a ref to the CreateEnterprise component
const createEnterpriseRef = ref<{ show: () => void } | null>(null)

type menuItem = {
  type?: string
  icon?: string
  name?: string
  link?: string
  action?: () => void
}

const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
const { manageBrand } = useManageBrand()

// 企业列表
const companyList = ref<CompanyItem[]>([])
// 企业列表加载状态
const companyListLoading = ref(false)

// 当前企业ID
const curCompanyId = computed(() => {
  return company.value.shopCompanyId
})

// 个人设置菜单
const personalMenus: Array<menuItem> = [
  {
    icon: 'i-a-user-outlined',
    name: '个人信息',
    link: route.path + '/info',
  },
  {
    icon: 'i-a-idcard-outlined',
    name: '职业信息',
    link: route.path + '/job',
  },
  {
    icon: 'i-a-star-outlined',
    name: '收藏夹',
    link: route.path + '/fav',
  },
  {
    icon: 'i-a-link-outlined',
    name: '账号绑定',
    link: route.path + '/account',
  },
  {
    icon: 'i-a-lock-outlined',
    name: '安全设置',
    link: route.path + '/security',
  },
]

// 企业管理菜单
const enterpriseMenus = computed(() => {
  const base: menuItem[] = []

  if (!company.value.shopCompanyId) return base

  const { type, status } = company.value

  // 开通设备商
  // if (type == 2 && status != 2 && status != 20) {
  //   base.push({
  //     icon: 'i-a-setting-outlined',
  //     name: '开通设备商',
  //     action: () => {
  //       Modal.confirm({
  //         title: '提示',
  //         content: '是否开通设备商？',
  //         onOk: async () => {
  //           const isAdmin = await isEnterpriseAdmin(user.value.userMobile, company.value.shopCompanyId)
  //           if (!isAdmin) {
  //             message.error('您不是企业管理员，无法成为设备商')
  //             return
  //           }
  //           const companyInfo = await getCompany(company.value.shopCompanyId)
  //           if (companyInfo.status == 1 && companyInfo.type == 3) {
  //             message.warn('企业已开通设备商，无需重复开通')
  //             return
  //           }
  //
  //           if (companyInfo.status == 3) {
  //             message.warn('当前企业正在审核中')
  //             return
  //           }
  //
  //           if (companyInfo.status == 30) {
  //             Modal.confirm({
  //               title: '提示',
  //               content: '您有审核失败的企业，是否前往修改审核信息',
  //               onOk: async () => {
  //                 router.push('/enterpriseCenter/device-join?companyId=' + companyInfo.shopCompanyId)
  //               },
  //             })
  //             return
  //           }
  //
  //           const [err, res] = await try_http('/mall/p/shopCompany/storage', {
  //             method: 'POST',
  //             body: {
  //               ...companyInfo,
  //               type: 1,
  //             },
  //           })
  //           if (!err) {
  //             message.success('设备商开通审核已提交')
  //           }
  //         },
  //       })
  //     },
  //   })
  // }

  // 开通供应商
  // if (type == 1 && status != 2 && status != 10) {
  //   base.push({
  //     icon: 'i-a-setting-outlined',
  //     name: '开通供应商',
  //     action: () => {
  //       Modal.confirm({
  //         title: '提示',
  //         content: '是否开通供应商？',
  //         onOk: async () => {
  //           const isAdmin = await isEnterpriseAdmin(user.value.userMobile, company.value.shopCompanyId)
  //           if (!isAdmin) {
  //             message.error('您不是企业管理员，无法开通供应商')
  //             return
  //           }
  //           const companyInfo = await getCompany(company.value.shopCompanyId)
  //           if (companyInfo.status == 1 && companyInfo.type == 3) {
  //             message.warn('企业已开通供应商，无需重复开通')
  //             return
  //           }
  //           if (companyInfo.status == 3) {
  //             message.warn('当前企业正在审核中')
  //             return
  //           }
  //           if (companyInfo.status == 40) {
  //             Modal.confirm({
  //               title: '提示',
  //               content: '您有审核失败的企业，是否前往修改审核信息',
  //               onOk: async () => {
  //                 window.open(`${bomai}/brand-join/settle?companyId=${companyInfo.shopCompanyId}&type=edit`, '_blank')
  //               },
  //             })
  //             return
  //           }
  //
  //           window.open(`${bomai}/brand-join/settle?companyId=${companyInfo.shopCompanyId}`, '_blank')
  //         },
  //       })
  //     },
  //   })
  // }

  return base
})

// 初始化企业列表
const initCompanyList = () => {
  if (!user.value.userMobile) return

  companyListLoading.value = true
  getMerchantByUser({
    userMobile: user.value.userMobile,
  })
    .then((res) => {
      if (res.data) {
        companyList.value = JSON.parse(res.data)
      }
    })
    .finally(() => {
      companyListLoading.value = false
    })
}

// 切换企业
const onSwitchCompany = (data) => {
  http('/mall/shop/userSpace/saveOrUpdate', {
    method: 'post',
    body: {
      userMobile: user.value.userMobile,
      merchantId: data.shopCompanyId,
      merchantName: data.merchantShortName || data.merchantName,
    },
  }).then((res) => {
    useMall(res, async (ret) => {
      message.success('切换成功')
      setCompany(data)
    })
  })
}

// 获取企业类型标签
const getCompanyTip = (item) => {
  const { type } = item
  switch (type) {
    case 1:
      return ['设备商']
    case 2:
      return ['供应商']
    case 3:
      return ['设备商', '供应商']
    default:
      return []
  }
}

// 处理企业菜单点击
const handleEnterpriseClick = (item: menuItem) => {
  item.action?.()
}

// 显示创建企业弹窗
const showCreateEnterpriseModal = () => {
  if (!user.value.userMobile) {
    message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
    return
  }
  if (createEnterpriseRef.value) {
    createEnterpriseRef.value.show()
  }
}

// 认证检查
const auth = (url) => {
  if (!user.value.userMobile) {
    message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
    return
  }
  router.push(url)
}

onMounted(() => {
  initCompanyList()
})
</script>

<style scoped></style>
