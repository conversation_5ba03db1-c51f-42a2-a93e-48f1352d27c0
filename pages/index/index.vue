<template>
  <!-- 英雄区 -->
  <section class="hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            自动化装备制造行业
            <br />
            AI供应链服务平台
          </h1>
          <p class="hero-subtitle">用AI技术重构供应链，为装备制造商研发采购全流程提供高效解决方案</p>
          <div class="hero-buttons">
            <a class="btn btn-primary btn-big" v-if="!isLogin" @click.prevent="startAiPurchase">
              开启AI采购之旅
              <loading-outlined v-if="startLoading"></loading-outlined>
            </a>
            <div class="btn btn-primary btn-big" v-if="isLogin" @click="goWorkSapce">
              {{ isPersonal || company.type == 2 ? '前往个人工作台' : '前往企业工作台' }}
            </div>
            <a href="#versions" class="enterprise-link" @click="scrollToVersions">了解企业版</a>
          </div>
        </div>
        <!-- <div class="hero-image">
          <img src="./assets/images/hero.png" alt="智能供应链平台示意图">
        </div> -->
      </div>
    </div>
    <div class="hero-shapes"></div>
    <!-- 视频背景 -->
    <div class="video-background">
      <video autoplay muted loop webkit-playsinline="true" playsinline="true">
        <source src="~/assets/video/bg.mp4" type="video/mp4" />
      </video>
    </div>
  </section>

  <!-- AI智能伙伴 -->
  <section class="ai-assistants" id="ai-assistants" data-aos="fade-up">
    <div class="container">
      <!-- 标题区域 -->
      <div class="section-title">
        <div class="title-wrapper">
          <div class="title-line"></div>
          <h2>认识我们的AI伙伴 - 小妍</h2>
          <div class="title-line"></div>
        </div>
      </div>

      <!-- 新的小妍介绍区域 -->
      <div class="yan-introduction">
        <!-- 工程师助手能力 -->
        <div class="capability-card engineering-card" data-aos="fade-up" data-aos-delay="100">
          <div class="card-icon">
            <i class="fas i-fa-cogs"></i>
          </div>
          <h3>工程师的得力助手</h3>
          <p>零部件百事通，帮助工程师下载图纸模型，了解规格参数，产品选型找我就对啦！</p>
        </div>

        <!-- 采购专家能力 -->
        <div class="capability-card procurement-card" data-aos="fade-up" data-aos-delay="200">
          <div class="card-icon">
            <i class="fas i-fa-cash-register"></i>
          </div>
          <h3>采购的智能参谋</h3>
          <p>精打细算专家，询价、下单、管订单，项目管理一站式服务，从需求分析到交付跟踪，全流程智能化管理！</p>
        </div>

        <!-- 小妍角色区域 -->
        <div class="yan-character" data-aos="fade-left" data-aos-delay="300">
          <div class="character-container">
            <div class="character-image" @click="goToYanChat">
              <img :src="currentYanImage" alt="小妍" />
              <div class="character-glow"></div>

              <!-- 智能聊天提示 - 默认显示click-hint，悬停时左移展开 -->
              <div
                class="smart-chat-indicator"
                :class="{ expanded: showChatBubble }"
                @mouseenter="startImageAnimation"
                @mouseleave="stopImageAnimation"
              >
                <!-- 点击提示 - 默认状态和展开状态合并 -->
                <div class="click-hint group" :class="{ expanded: showChatBubble }" @click="goToYanChat">
                  <i class="fas i-fa-comment-dots absolute group-hover:relative" />

                  <!-- 展开状态文本内容 -->
                  <div class="hint-content" :class="{ show: showChatBubble }">
                    <span class="greeting">戳我，开始和我聊天吧～</span>
                  </div>
                </div>

                <!-- 脉冲环效果 -->
                <div class="pulse-ring" :class="{ hide: showChatBubble }"></div>
                <div class="pulse-ring-2" :class="{ hide: showChatBubble }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 应用场景 -->
  <section class="core-sections" id="core-sections" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>应用场景</h2>
      </div>
      <div class="core-grid">
        <div class="core-card" data-aos="zoom-in" data-aos-delay="100">
          <div class="core-icon">
            <i class="fas i-fa-lightbulb"></i>
          </div>
          <h3 class="core-title">研发环节</h3>
          <p class="core-desc">
            提供BOM优化建议，零部件标准化方案，大幅缩短研发周期，降低设计成本。集成CAD/CAM系统，实现设计与采购无缝对接。
          </p>
          <!-- <a href="#" class="btn btn-outline">了解详情</a> -->
        </div>
        <div class="core-card" data-aos="zoom-in" data-aos-delay="200">
          <div class="core-icon">
            <i class="fas i-fa-shopping-cart"></i>
          </div>
          <h3 class="core-title">采购环节</h3>
          <p class="core-desc">
            智能采购决策系统，自动比价和供应商评估，一键下单和批量处理。实时市场价格监控，把握最佳采购时机，降低采购总成本。
          </p>
          <!-- <a href="#" class="btn btn-outline">了解详情</a> -->
        </div>
        <div class="core-card" data-aos="zoom-in" data-aos-delay="300">
          <div class="core-icon">
            <i class="fas i-fa-headset"></i>
          </div>
          <h3 class="core-title">售后环节</h3>
          <p class="core-desc">
            结合AI智能客服与专属人工客服的双重支持，AI提供全天候7*24小时即时响应，专属客服经理提供一对一专业解决方案，智能与温度并存，全面提升服务体验。
          </p>
          <!-- <a href="#" class="btn btn-outline">了解详情</a> -->
        </div>
      </div>
    </div>
  </section>

  <!-- 特性区 -->
  <section class="features" id="features" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>平台核心优势</h2>
      </div>
      <div class="feature-grid">
        <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
          <div class="feature-content">
            <div class="feature-icon">
              <i class="fas i-fa-robot"></i>
            </div>
            <h3 class="feature-title">AI智能匹配</h3>
            <p class="feature-desc">基于先进算法，精准匹配您的采购需求与优质供应商资源，大幅提升采购效率与质量。</p>
          </div>
        </div>
        <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
          <div class="feature-content">
            <div class="feature-icon">
              <i class="fas i-fa-search-dollar"></i>
            </div>
            <h3 class="feature-title">成本优化</h3>
            <p class="feature-desc">
              通过供应链集采及智能议价，降低BOM里的每颗物料采购成本，帮助集成商平均降低5%零部件采购成本。
            </p>
          </div>
        </div>
        <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
          <div class="feature-content">
            <div class="feature-icon">
              <i class="fas i-fa-chart-line"></i>
            </div>
            <h3 class="feature-title">数据分析</h3>
            <p class="feature-desc">全面的供应链数据分析，实时监控关键指标，为决策提供可靠依据和预测支持。</p>
          </div>
        </div>
        <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
          <div class="feature-content">
            <div class="feature-icon">
              <i class="fas i-fa-shield-alt"></i>
            </div>
            <h3 class="feature-title">质量保障</h3>
            <p class="feature-desc">严格的供应商资质审核和产品质量监控体系，确保每一个零部件都符合标准。</p>
          </div>
        </div>
        <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
          <div class="feature-content">
            <div class="feature-icon">
              <i class="fas i-fa-bolt"></i>
            </div>
            <h3 class="feature-title">响应迅速</h3>
            <p class="feature-desc">平均响应时间缩短70%，急件处理最快1小时内完成，有效应对紧急需求。</p>
          </div>
        </div>
        <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
          <div class="feature-content">
            <div class="feature-icon">
              <i class="fas i-fa-sync-alt"></i>
            </div>
            <h3 class="feature-title">全流程透明</h3>
            <p class="feature-desc">从询价到交付的全过程可视化管理，随时了解订单状态和物流信息。</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="version" id="version" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>版本功能</h2>
      </div>

      <!-- 版本功能总结卡片 -->
      <div class="version-summary-cards" data-aos="fade-up" data-aos-delay="100">
        <div class="summary-card personal-summary">
          <div class="card-header">
            <div class="plan-badge personal">
              <i class="fas i-fa-user"></i>
              <span>个人版</span>
            </div>
          </div>
          <div class="card-content">
            <div class="feature-item">
              <p>零部件库检索 + 小妍AI助手，快速找到合适的零部件</p>
            </div>
            <div class="feature-item">
              <p>连接优质供应商，获取物料询价（每日10种物料限制）</p>
            </div>
          </div>
          <div class="card-footer">
            <button
              class="activate-btn personal-btn"
              :class="{ activated: isLogin }"
              :disabled="isLogin"
              @click="openPersonalVersion"
            >
              <i class="fas" :class="isLogin ? 'i-fa-check' : 'i-fa-rocket'"></i>
              {{ isLogin ? '已开通' : '立即开通' }}
            </button>
          </div>
        </div>

        <div class="summary-card enterprise-summary">
          <div class="card-header">
            <div class="plan-badge enterprise">
              <i class="fas i-fa-crown"></i>
              <span>企业版</span>
            </div>
            <div class="popular-badge">推荐</div>
          </div>
          <div class="card-content">
            <div class="feature-item">
              <p>个人版全部功能</p>
            </div>
            <div class="feature-item">
              <p>采购BOM管理、不限次询价、下单、对账、付款一站式解决方案</p>
            </div>
            <div class="feature-item">
              <p>专属采购助理 + AI客服，提供专业采购咨询服务</p>
            </div>
            <div class="feature-item">
              <p>灵活的账期管理，优化企业现金流</p>
            </div>
          </div>
          <div class="card-footer">
            <button
              class="activate-btn enterprise-btn"
              :class="{ activated: isDevice }"
              :disabled="isDevice"
              @click="openEnterpriseVersion"
            >
              <i class="fas" :class="isDevice ? 'i-fa-check' : 'i-fa-rocket'"></i>
              {{ isDevice ? '已开通' : '立即开通' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 版本功能对比表格 -->
      <div class="version-comparison-container">
        <div class="version-table">
          <!-- 表格头部 -->
          <div class="table-header">
            <div class="module-column">
              <h3>系统模块</h3>
            </div>
            <div class="feature-column">
              <h3>功能特性</h3>
            </div>
            <div class="personal-column">
              <div class="plan-badge personal">
                <i class="fas i-fa-user"></i>
                <span>个人版</span>
              </div>
            </div>
            <div class="enterprise-column">
              <div class="plan-badge enterprise">
                <i class="fas i-fa-crown"></i>
                <span>企业版</span>
              </div>
            </div>
          </div>

          <!-- 表格内容 -->
          <div class="table-body">
            <!-- 零部件库 -->
            <div class="module-group">
              <div class="module-row">
                <div class="module-name">
                  <i class="fas i-fa-database"></i>
                  <span>零部件库</span>
                </div>
                <div class="feature-name">零部件库产品检索</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">小妍AI助手（选型）</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <!-- <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">专属零部件库</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">标准件产品管理</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">供应商管理</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
              </div> -->
            </div>

            <!-- 采购平台 -->
            <div class="module-group">
              <div class="module-row">
                <div class="module-name">
                  <i class="fas i-fa-shopping-cart"></i>
                  <span>采购平台</span>
                </div>
                <div class="feature-name">采购BOM管理</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <!-- <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">非标2D/3D图纸共享</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
              </div> -->
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">供应商寻源</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">物料询价</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="limited"><i class="fas i-fa-exclamation-circle"></i></span>
                  <div class="feature-desc">每日限10种物料</div>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                  <div class="feature-desc">无限制</div>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">采购下单</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">采购对账</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">采购付款</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">退换货</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
            </div>

            <!-- 平台支持 -->
            <div class="module-group">
              <div class="module-row">
                <div class="module-name">
                  <i class="fas i-fa-headset"></i>
                  <span>平台支持</span>
                </div>
                <div class="feature-name">研选供应商寻源</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">账期结算</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">小妍AI助手（平台客服）</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">专属采购助理</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="unavailable"><i class="fas i-fa-times-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
            </div>

            <!-- 积分权益 -->
            <!-- <div class="module-group">
              <div class="module-row">
                <div class="module-name">
                  <i class="fas i-fa-gift"></i>
                  <span>积分权益</span>
                </div>
                <div class="feature-name">帮淘价赚积分</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="limited"><i class="fas i-fa-exclamation-circle"></i></span>
                  <div class="feature-desc">单次最多50积分</div>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                  <div class="feature-desc">无上限</div>
                </div>
              </div>
              <div class="feature-row">
                <div class="empty-module"></div>
                <div class="feature-name">积分兑换</div>
                <div class="personal-feature" data-label="个人账号">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
                <div class="enterprise-feature" data-label="设备商团队版">
                  <span class="available"><i class="fas i-fa-check-circle"></i></span>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 注册区 -->
  <!-- <section class="register" id="register" data-aos="fade-left">
    <div class="container">
      <div class="register-container">
        <div class="register-image">
          <img src="https://via.placeholder.com/600x500/2a2a35/ffffff?text=Register+Now" alt="企业入驻示意图">
        </div>
        <div class="register-content">
          <h2 class="register-title">企业入驻，开启AI供应链之旅</h2>
          <p class="register-desc">加入研选工场，即刻体验数字化转型带来的降本增效，让您的企业在激烈的市场竞争中脱颖而出。</p>
          <div class="register-points">
            <div class="register-point">
              <i class="fas i-fa-check-circle"></i>
              <p>快速注册，3分钟完成企业认证</p>
            </div>
            <div class="register-point">
              <i class="fas i-fa-check-circle"></i>
              <p>专属客户经理一对一服务</p>
            </div>
            <div class="register-point">
              <i class="fas i-fa-check-circle"></i>
              <p>30天免费试用全部高级功能</p>
            </div>
            <div class="register-point">
              <i class="fas i-fa-check-circle"></i>
              <p>7*24小时技术支持</p>
            </div>
          </div>
          <div class="register-buttons">
            <a href="#" class="btn btn-primary">立即入驻</a>
            <a href="#" class="btn btn-outline">预约演示</a>
          </div>
        </div>
      </div>
    </div>
  </section> -->

  <!-- 插件 -->
  <!-- <section class="plugin" id="plugin" data-aos="fade-left">
    <div class="container">
      <div class="section-title">
        <h2>SolidWorks 插件</h2>
      </div>
      <div class="plugin-container">
        <div class="plugin-image" data-aos="fade-right">
          <img src="~/assets/images/plugin.png" alt="SolidWorks插件界面" />
        </div>
        <div class="plugin-content" data-aos="fade-left" data-aos-delay="200">
          <p class="plugin-desc">
            研选工场SolidWorks插件为您的设计工作提供强大支持，让零部件选型与设计过程完美融合。无需在多个平台间切换，即可在SolidWorks环境中轻松访问、搜索和导入所需零部件。
          </p>
          <div class="plugin-features">
            <div class="plugin-feature">
              <i class="fas i-fa-search"></i>
              <div>
                <h4>智能搜索</h4>
                <p>直接在SolidWorks界面中搜索平台全部零部件库，包括3D模型、参数和供应信息</p>
              </div>
            </div>
            <div class="plugin-feature">
              <i class="fas i-fa-cloud-download-alt"></i>
              <div>
                <h4>一键导入</h4>
                <p>无需手动下载，一键将3D模型导入当前设计环境，节省大量时间</p>
              </div>
            </div>
            <div class="plugin-feature">
              <i class="fas i-fa-sync-alt"></i>
              <div>
                <h4>实时更新</h4>
                <p>零部件数据实时同步，确保您始终使用最新的模型和信息</p>
              </div>
            </div>
            <div class="plugin-feature">
              <i class="fas i-fa-cart-plus"></i>
              <div>
                <h4>快速下单</h4>
                <p>从设计环境直接提交采购需求，缩短从设计到采购的流程</p>
              </div>
            </div>
          </div>
          <div class="plugin-buttons">
            <div class="btn btn-primary" @click="download">
              下载插件
              <loading-outlined v-if="donwloadLoading"></loading-outlined>
            </div>
            <div class="btn btn-outline" @click="goHistory">历史版本</div>
          </div>
        </div>
      </div>
    </div>
  </section> -->

  <!-- 合作伙伴 -->
  <!-- <section class="partners" id="partners" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>合作伙伴</h2>
        <p>已有超过500家自动化装备制造商选择我们的平台</p>
      </div>
      <div class="partners-logo">
        <img class="partner-item" src="https://via.placeholder.com/140x70/3f3f4a/b0b0c0?text=Partner+1" alt="合作伙伴">
        <img class="partner-item" src="https://via.placeholder.com/140x70/3f3f4a/b0b0c0?text=Partner+2" alt="合作伙伴">
        <img class="partner-item" src="https://via.placeholder.com/140x70/3f3f4a/b0b0c0?text=Partner+3" alt="合作伙伴">
        <img class="partner-item" src="https://via.placeholder.com/140x70/3f3f4a/b0b0c0?text=Partner+4" alt="合作伙伴">
        <img class="partner-item" src="https://via.placeholder.com/140x70/3f3f4a/b0b0c0?text=Partner+5" alt="合作伙伴">
        <img class="partner-item" src="https://via.placeholder.com/140x70/3f3f4a/b0b0c0?text=Partner+6" alt="合作伙伴">
      </div>
    </div>
  </section> -->

  <!-- <MemberGuide :visible="showMembershipModal" @close="closeMembershipModal" @upgrade="handleMembershipUpgrade" /> -->
  <EnterpriseRegistrationModal
    :visible="showEnterpriseModal"
    @close="closeEnterpriseModal"
    @submit="handleEnterpriseSubmit"
  />

  <!-- 页脚 -->

  <!-- 回到顶部按钮 -->
  <div class="back-to-top" :class="{ visible: showBackToTop }" @click="scrollToTop" title="回到顶部">
    <i class="fas i-fa-arrow-up"></i>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { getShopsByCompanyId, isEnterpriseAdmin, _http } from '~/api/mall-platform'
import { getCompany } from '~/api/mall-manage'
import fileDownload from 'js-file-download'
import MemberGuide from '~/components/MemberGuide/index.vue'
const { isPersonal, company } = storeToRefs(companyStore())
import EnterpriseRegistrationModal from '~/components/EnterpriseRegistrationModal/index.vue'
const user = computed(() => userStore().user)
const showMembershipModal = ref(true)
const showEnterpriseModal = ref(false)

// 小妍聊天气泡状态
const showChatBubble = ref(false)

// 小妍图片切换状态
const currentYanImage = ref('/yan_silent.png')
const isImageAnimating = ref(false)
let imageAnimationTimer = null

// 回到顶部功能
const showBackToTop = ref(false)

// 倒计时状态
const countdown = ref({
  days: '00',
  hours: '00',
  minutes: '00',
  seconds: '00',
})

let countdownTimer = null

// 计算倒计时
const updateCountdown = () => {
  // 设置活动结束时间（当前时间 + 7天）
  const endTime = new Date()
  endTime.setDate(endTime.getDate() + 7)

  const now = new Date().getTime()
  const distance = endTime.getTime() - now

  if (distance > 0) {
    const days = Math.floor(distance / (1000 * 60 * 60 * 24))
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((distance % (1000 * 60)) / 1000)

    countdown.value = {
      days: String(days).padStart(2, '0'),
      hours: String(hours).padStart(2, '0'),
      minutes: String(minutes).padStart(2, '0'),
      seconds: String(seconds).padStart(2, '0'),
    }
  } else {
    // 倒计时结束
    countdown.value = {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
    }
  }
}

definePageMeta({
  layout: 'page',
})

// 版本功能卡片点击处理
const openPersonalVersion = () => {
  // 跳转到个人版开通页面或显示开通流程
  if (!isLogin.value) {
    navigateTo('/register')
    return
  }
}

const openEnterpriseVersion = () => {
  if (!isLogin.value) {
    navigateTo('/register')
    return
  }

  if (!isDevice.value) {
    navigateTo('/enterpriseCenter/create')
    return
  }
  // 如果已登录且已有企业，显示企业升级模态框
  if (user.value && company.value) {
    showEnterpriseModal.value = true
  } else {
    // 否则跳转到企业注册页面
    navigateTo('/enterpriseCenter/create')
  }
}

const join = async () => {
  const isAdmin = await isEnterpriseAdmin(user.value.userMobile, company.value.shopCompanyId)
  if (!isAdmin) {
    message.error('您不是企业管理员，无法入驻设备商')
    return
  }
  const companyInfo = await getCompany(company.value.shopCompanyId)
  if (companyInfo.status == 1 && companyInfo.type == 3) {
    message.warn('您所在的企业已经入驻设备商，无需重复入驻')
    return
  }

  if (companyInfo.status == 3) {
    message.warn('当前企业正在审核中')
    return
  }

  if (companyInfo.status == 30) {
    Modal.confirm({
      title: '提示',
      content: '您有审核失败的企业，是否前往修改审核信息',
      onOk: async () => {
        router.push('/enterpriseCenter/device-join?companyId=' + companyInfo.shopCompanyId)
      },
    })
    return
  }

  const [err, res] = await try_http('/mall/p/shopCompany/storage', {
    method: 'POST',
    body: {
      ...companyInfo,
      type: 1,
    },
  })
  if (!err) {
    message.success('设备商入驻审核已提交')
  }
}
const loading = ref(false)
const handleJoin = async () => {
  if (loading.value) return
  if (!isLogin.value) {
    navigateTo({
      path: '/login',
      query: {
        redirect: '/#footer',
      },
    })
  } else {
    const { type } = company.value
    if (type == 1 || type == 3) {
      loading.value = true
      await join()
      loading.value = false
    } else {
      if (!user.value.userMobile) {
        message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
        return
      }
      navigateTo('/enterpriseCenter/create')
    }
  }
}

// 个人版入驻
const handlePersonalJoin = async () => {
  if (!isLogin.value) {
    navigateTo({
      path: '/login',
      query: {
        redirect: '/#footer',
      },
    })
  } else {
    // 个人版直接跳转到聊天页面
    navigateTo('/chat')
  }
}

// 跳转到小妍聊天页面
const goToYanChat = () => {
  navigateTo('/yan')
}

// 开始图片动画
const startImageAnimation = () => {
  showChatBubble.value = true
  isImageAnimating.value = true

  // 立即切换一次
  currentYanImage.value = currentYanImage.value === '/yan.png' ? '/yan_silent.png' : '/yan.png'

  // 设置定时器，每0.3秒切换一次
  imageAnimationTimer = setInterval(() => {
    currentYanImage.value = currentYanImage.value === '/yan.png' ? '/yan_silent.png' : '/yan.png'
  }, 300)
}

// 停止图片动画
const stopImageAnimation = () => {
  showChatBubble.value = false
  isImageAnimating.value = false

  // 清除定时器
  if (imageAnimationTimer) {
    clearInterval(imageAnimationTimer)
    imageAnimationTimer = null
  }

  // 恢复到默认图片
  currentYanImage.value = '/yan_silent.png'
}

// 企业版入驻
const handleEnterpriseJoin = async () => {
  if (!isLogin.value) {
    navigateTo({
      path: '/login',
      query: {
        redirect: '/#footer',
      },
    })
  } else {
    showEnterpriseModal.value = true
  }
}

// 关闭企业入驻模态框
const closeEnterpriseModal = () => {
  showEnterpriseModal.value = false
}

// 处理企业入驻表单提交
const handleEnterpriseSubmit = async (formData) => {
  try {
    // 这里可以调用API提交企业入驻申请
    console.log('企业入驻申请数据:', formData)

    // 模拟API调用
    // const [err, res] = await _try(() =>
    //   _http.post('/enterprise/apply', formData)
    // )

    // if (!err) {
    message.success('企业入驻申请已提交，我们会尽快联系您！')
    closeEnterpriseModal()
    // }
  } catch (error) {
    console.error('提交企业入驻申请失败:', error)
    message.error('提交失败，请稍后重试')
  }
}

const donwloadLoading = ref(false)
const download = async () => {
  if (donwloadLoading.value) return
  donwloadLoading.value = true
  const [err, res] = await _try(() =>
    _http.get('/shop/pluginVersion/page', {
      status: 1,
    })
  )
  donwloadLoading.value = false
  if (!err) {
    const data = parseJson(res.data, {})
    if (data.records?.length) {
      const file = data.records[0]
      const { filePath, version } = file
      $fetch(useObs(filePath), {
        responseType: 'blob',
      }).then((res) => {
        const ext = filePath.split('.').at(-1)
        fileDownload(res, `YanXuanForSWInstaller_${version}.${ext}`)
      })
    }
  } else {
    console.error('下载失败', err)
  }
}

const goHistory = () => {
  window.open(bomai + '/plugin-history', '_blank')
}

const { manageBrand } = useManageBrand(company)

const applySupplier = async () => {
  if (!isLogin.value) {
    navigateTo('/login')
  } else if (!company.value.shopCompanyId) {
    if (!user.value.userMobile) {
      message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
      return
    }
    Modal.confirm({
      title: '提示',
      content: '您当前处于个人空间，将创建新的企业，是否继续',
      onOk() {
        window.open(`${bomai}/brand-join/settle`, '_blank')
      },
    })
  } else {
    const isAdmin = await isEnterpriseAdmin(user.value.userMobile, company.value.shopCompanyId)
    if (!isAdmin) {
      message.error('您不是企业管理员，无法开通供应商')
      return
    }
    const companyInfo = await getCompany(company.value.shopCompanyId)
    if (companyInfo.status == 1 && (companyInfo.type == 3 || companyInfo.type == 2)) {
      message.warn('企业已开通供应商，无需重复开通')
      return
    }
    if (companyInfo.status == 3) {
      message.warn('当前企业正在审核中')
      return
    }
    if (companyInfo.status == 40) {
      Modal.confirm({
        title: '提示',
        content: '您有审核失败的企业，是否前往修改审核信息',
        onOk: async () => {
          window.open(`${bomai}/brand-join/settle?companyId=${companyInfo.shopCompanyId}&type=edit`, '_blank')
        },
      })
      return
    }

    window.open(`${bomai}/brand-join/settle?companyId=${companyInfo.shopCompanyId}`, '_blank')
  }
}

const isDevice = computed(() => {
  return isLogin.value && eq(company.value.type, 1, 3)
})

const isSupplier = computed(() => {
  return isLogin.value && eq(company.value.type, 2, 3)
})

const startLoading = ref(false)
const isLogin = useLoginState()
const startAiPurchase = async () => {
  navigateTo('/login')
  // if (startLoading.value) return
  //
  // if (!isLogin.value) {
  //   navigateTo('/login')
  //   return
  // }
  //
  // if (!user.value.userMobile) {
  //   message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
  //   return
  // }
  //
  // if (!company.value.shopCompanyId) {
  //   navigateTo('/enterpriseCenter/create')
  //   return
  // }
  //
  // startLoading.value = true
  // await join()
  // startLoading.value = false
}

const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
const goWorkSapce = () => {
  if (company.value.type == 2) {
    message.info('请先在[个人中心]中切换至个人空间')
    return
  }
  window.open(`${bomai}/workSpace`, '_blank')
}

const closeMembershipModal = () => {
  showMembershipModal.value = false
}

const handleMembershipUpgrade = () => {
  showMembershipModal.value = false
}

// 回到顶部功能
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  })
}

// 滚动到版本功能区域
const scrollToVersions = () => {
  const versionSection = document.getElementById('version')
  if (versionSection) {
    versionSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

// 监听滚动事件
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 初始化倒计时
onMounted(() => {
  updateCountdown()
  countdownTimer = setInterval(updateCountdown, 1000)
  // 添加滚动事件监听
  window.addEventListener('scroll', handleScroll)
})

// 清理倒计时
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
  // 清理图片动画定时器
  if (imageAnimationTimer) {
    clearInterval(imageAnimationTimer)
  }
  // 移除滚动事件监听
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 80px;
}

/* 导航栏 */
header {
  position: fixed;
  width: 100%;
  z-index: 1000;
  top: 0;
  transition: background-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out, border-bottom 0.3s ease-in-out;

  &.scrolled {
    background-color: rgba(30, 30, 38, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
  }

  .logo {
    width: 200px;
    height: 32px;
    display: flex;
    align-items: center;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
    }
  }

  .nav-links {
    display: flex;
    gap: 40px;

    a {
      text-decoration: none;
      color: var(--light-gray);
      font-weight: 500;
      transition: color 0.3s;

      &:hover {
        color: var(--primary);
      }
    }
  }

  .nav-buttons {
    display: flex;
    gap: 20px;

    /* Hide desktop buttons if mobile menu is open */
    .mobile-menu.is-open ~ .container & {
      opacity: 0;
      pointer-events: none;
    }

    .login-buttons {
      display: flex;
      align-items: center;
      gap: 20px;
    }
  }
}

.btn {
  padding: 10px 24px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: left 0.3s ease-in-out;
    z-index: -1;
  }

  &:hover::before {
    left: 0;
  }

  &.btn-big {
    padding: 14px 32px;
    font-size: 20px;
  }
}

.btn-primary {
  background-color: var(--primary);
  color: var(--light);
  border-color: var(--primary);

  &:hover {
    background-color: #e03a20;
    border-color: #e03a20;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 76, 48, 0.4);
  }
}

.btn-outline {
  background-color: transparent;
  color: var(--light);
  border: 1px solid var(--light-gray);

  &:hover {
    background-color: var(--primary);
    color: var(--light);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 76, 48, 0.4);
  }
}

.btn-link {
  background-color: transparent;
  color: var(--light);
  border: none;
  padding: 10px 15px;
  line-height: 1.5;

  &:hover {
    color: var(--primary);
  }
}

/* 英雄区 */
.hero {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--dark) 100%);
  color: var(--light);
  padding-top: 88px;
  height: 100vh;
  // max-height: 1080px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;

  .hero-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 5;
    /* Increased z-index to place above video background */
  }

  .hero-text {
    flex: 1;
    padding-right: 30px;
  }

  .hero-title {
    font-size: 52px;
    font-weight: bold;
    margin-bottom: 24px;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInDown 1s ease-out;
  }

  .hero-subtitle {
    font-size: 20px;
    margin-bottom: 40px;
    line-height: 1.6;
    color: var(--light-gray);
    animation: fadeInUp 1s 0.3s ease-out backwards;
  }

  .hero-buttons {
    display: flex;
    gap: 32px;
    animation: fadeInUp 1s 0.6s ease-out backwards;

    .btn-outline-light {
      // Moved specific button style here
      background-color: transparent;
      color: var(--light);
      border: 1px solid var(--light);

      &:hover {
        background-color: var(--light);
        color: var(--primary);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
      }

      &:hover::before {
        display: none;
      }
    }

    .enterprise-link {
      color: var(--primary);
      text-decoration: none;
      font-size: 18px;
      font-weight: 600;
      padding: 18px 0;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
      cursor: pointer;
      display: inline-block;

      &:hover {
        color: var(--primary-dark);
        text-decoration: none;
      }
    }
  }

  .hero-image {
    flex: 1;
    text-align: center;
    align-self: center;

    img {
      max-width: 100%;
      height: auto;
      max-height: calc(100vh - 150px);
      border-radius: 10px;
      animation: zoomIn 1s 0.5s ease-out backwards;
      object-fit: contain;
    }
  }

  .hero-shapes {
    position: absolute;
    top: -100px;
    left: -100px;
    height: 500px;
    background: radial-gradient(circle, rgba(249, 76, 48, 0.1) 0%, rgba(249, 76, 48, 0) 70%);
    border-radius: 50%;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      bottom: -150px;
      right: -150px;
      height: 600px;
      background: radial-gradient(circle, rgba(42, 42, 53, 0.1) 0%, rgba(42, 42, 53, 0) 70%);
      border-radius: 50%;
    }
  }

  /* 视频背景样式 */
  .video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(30, 30, 38, 0.7);
      /* 添加暗色遮罩增强内容可读性 */
      z-index: 1;
    }

    video {
      position: absolute;
      min-width: 100%;
      min-height: 100%;
      width: auto;
      height: auto;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      object-fit: cover;
    }
  }
}

/* Section Base Styles */
.section-title {
  text-align: center;
  margin-bottom: 60px;

  h2 {
    font-size: 36px;
    color: var(--light);
    margin-bottom: 16px;
  }

  p {
    font-size: 18px;
    color: var(--light-gray);
    max-width: 800px;
    margin: 0 auto;
  }
}

/* 特性区 */
.features {
  padding: 100px 0;
  background-color: var(--dark);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 60px;
  }

  .feature-card {
    background-color: var(--secondary);
    border-radius: 10px;
    padding: 40px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease-in-out;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(249, 76, 48, 0.1) 0%, rgba(249, 76, 48, 0) 70%);
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.5s, transform 0.5s ease-out;
      z-index: 1;
    }

    &:hover {
      transform: translateY(-10px) scale(1.02) perspective(1000px) rotateX(2deg) rotateY(-2deg);
      box-shadow: 0 20px 40px rgba(249, 76, 48, 0.25);
      border-color: var(--primary);

      &::before {
        opacity: 1;
      }

      .feature-icon {
        transform: scale(1.1) rotate(10deg);
      }
    }

    .feature-content {
      position: relative;
      z-index: 2;
    }

    .feature-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 70px;
      height: 70px;
      border-radius: 20px;
      background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
      margin-bottom: 24px;
      box-shadow: 0 4px 10px rgba(249, 76, 48, 0.3);
      transition: transform 0.3s ease-in-out;

      i {
        font-size: 28px;
        color: var(--light);
      }
    }

    .feature-title {
      font-size: 22px;
      color: var(--light);
      margin-bottom: 16px;
    }

    .feature-desc {
      font-size: 16px;
      color: var(--light-gray);
      line-height: 1.6;
    }
  }
}

/* AI智能伙伴区 */
.ai-assistants {
  padding: 120px 0 0 0;
  background: var(--dark);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;

  /* 背景装饰 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 20%, rgba(var(--primary-rgb), 0.08) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(var(--primary-rgb), 0.03) 0%, transparent 50%),
      linear-gradient(45deg, rgba(var(--primary-rgb), 0.02) 0%, transparent 100%);
    pointer-events: none;
  }

  /* 几何装饰元素 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 15% 30%, rgba(var(--primary-rgb), 0.05) 2px, transparent 2px),
      radial-gradient(circle at 85% 70%, rgba(var(--primary-rgb), 0.03) 1px, transparent 1px),
      linear-gradient(45deg, transparent 40%, rgba(var(--primary-rgb), 0.02) 50%, transparent 60%);
    background-size: 200px 200px, 150px 150px, 300px 300px;
    pointer-events: none;
    opacity: 0.6;
  }

  /* 标题区域 */
  .section-title {
    text-align: center;
    margin-bottom: 0px;

    .title-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;

      .title-line {
        width: 60px;
        height: 2px;
        background: linear-gradient(90deg, transparent, var(--primary), transparent);
      }

      h2 {
        color: var(--light);
        font-size: 36px;
        font-weight: 700;
        margin: 0;
        background: linear-gradient(45deg, var(--light), var(--primary));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        color: var(--light-gray);
        font-size: 18px;
        margin: 0;
        max-width: 600px;
        line-height: 1.6;
        text-align: center;
        position: relative;
        padding: 15px 30px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 25px;
        border: 1px solid rgba(var(--primary-rgb), 0.2);
        backdrop-filter: blur(10px);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, rgba(var(--primary-rgb), 0.05), transparent);
          border-radius: 25px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover::before {
          opacity: 1;
        }
      }
    }
  }

  /* 小妍介绍区域 */
  .yan-introduction {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    align-items: start;
  }

  /* 单个能力卡片 */
  .capability-card {
    margin-top: 80px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      border-color: rgba(255, 255, 255, 0.3);

      &::before {
        opacity: 1;
      }

      .card-icon {
        transform: scale(1.1);
        color: white;
      }
    }

    .card-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      transition: all 0.3s ease;

      i {
        font-size: 24px;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    h3 {
      color: var(--light);
      font-size: 20px;
      font-weight: 600;
      margin: 0 0 15px 0;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    p {
      color: var(--light-gray);
      line-height: 1.6;
      margin: 0;
      font-size: 15px;
    }
  }

  /* 小妍角色区域 */
  .yan-character {
    position: relative;
    display: flex;
    align-items: flex-end;
    height: 100%;
  }

  .character-container {
    width: 100%;
    position: relative;
  }

  .character-image {
    position: relative;
    width: 350px;
    height: 450px;
    margin: 0 auto;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      .character-glow {
        opacity: 1;
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: bottom;
      filter: drop-shadow(0 15px 25px rgba(0, 0, 0, 0.4));
      transition: all 0.3s ease;
    }

    .character-glow {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 250px;
      height: 120px;
      background: radial-gradient(ellipse, rgba(var(--primary-rgb), 0.3) 0%, transparent 70%);
      border-radius: 50%;
      opacity: 0.6;
      transition: all 0.3s ease;
      animation: gentle-pulse 3s ease-in-out infinite;
    }

    /* 智能聊天提示器 - 位于头像左侧，扩大触发区域 */
    .smart-chat-indicator {
      position: absolute;
      top: 10px;
      left: -240px; /* 扩大到左移后的位置 */
      width: 280px; /* 覆盖整个左移范围 */
      height: 45px;
      z-index: 15;

      /* 点击提示 - 支持展开 */
      .click-hint {
        position: absolute;
        top: 0;
        right: 0; /* 默认在右侧（smart-chat-indicator的右边界） */
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center; /* 确保图标居中 */
        background: linear-gradient(135deg, #f94c30, #ff8e8e);
        transition: all 0.6s ease;
        z-index: 2;
        overflow: hidden;

        i.fas {
          /* 图标样式 */
          font-size: 20px;
          color: white;
          transform: scaleX(-1);
        }

        /* 默认状态：圆形图标 */
        &:not(.expanded) {
          width: 50px;
          border-radius: 50%;
          box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
          animation: bounce-hint 2s infinite;
        }

        /* 展开状态：左移并展开为圆角矩形 */
        &.expanded {
          width: 220px;
          border-radius: 30px;
          animation: none;
          backdrop-filter: blur(15px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          justify-content: flex-start;
          gap: 12px;
          padding: 12px 12px;
        }

        /* 展开状态文本内容 */
        .hint-content {
          display: flex;
          flex-direction: column;
          gap: 4px;
          color: white;
          opacity: 0;
          transform: translateX(10px);
          transition: all 0.6s ease;
          pointer-events: none;
          font-size: 14px;
          font-weight: 500;

          &.show {
            opacity: 1;
            transform: translateX(0);
            pointer-events: auto;
          }

          .greeting {
            font-size: 15px;
            font-weight: 600;
            line-height: 1.2;
            white-space: nowrap;
          }

          .action-text {
            font-size: 13px;
            opacity: 0.9;
            line-height: 1.2;
            white-space: nowrap;
          }
        }
      }

      /* 脉冲环效果 */
      .pulse-ring,
      .pulse-ring-2 {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        border: 2px solid rgba(255, 107, 107, 0.6);
        border-radius: 50%;
        animation: pulse-ring 2s infinite;
        opacity: 1;
        transition: all 0.3s ease;

        &.hide {
          opacity: 0 !important;
          transform: translate(-50%, -50%) scale(0) !important;
          animation: none !important;
        }
      }

      .pulse-ring {
        width: 55px;
        height: 55px;
        right: -30px;
        animation-delay: 0s;
      }

      .pulse-ring-2 {
        width: 65px;
        height: 65px;
        right: -40px;
        animation-delay: 0.5s;
      }
    }

    /* 动画效果 */
    @keyframes bounce-hint {
      0%,
      20%,
      50%,
      80%,
      100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-8px);
      }
      60% {
        transform: translateY(-4px);
      }
    }

    @keyframes pulse-icon {
      0%,
      100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
    }

    @keyframes pulse-ring {
      0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
      }
      100% {
        transform: translate(-50%, -50%) scale(1.4);
        opacity: 0;
      }
    }

    @keyframes float-icon {
      0%,
      100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-3px);
      }
    }
  }
}

/* 版本功能区样式 */
.version {
  padding: 100px 0;
  background-color: var(--secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  /* 版本功能总结卡片样式 */
  .version-summary-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 200px;
    margin: 50px auto 60px;
    max-width: 1200px;
    padding: 0 20px;

    .summary-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(20px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-height: 300px;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.25);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary), var(--accent));
      }

      .card-header {
        padding: 25px 25px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .plan-badge {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 12px;
          font-weight: 600;
          font-size: 14px;

          &.personal {
            background: linear-gradient(135deg, #f94c30, #e63946);
            color: white;
          }

          &.enterprise {
            background: linear-gradient(135deg, #f94c30, #e63946);
            color: white;
          }

          i {
            font-size: 16px;
          }
        }

        .popular-badge {
          background: linear-gradient(135deg, #ffca46, #f94c30);
          color: white;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          animation: pulse 2s infinite;
        }
      }

      .card-content {
        padding: 20px 25px;
        flex: 1;

        .feature-item {
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            line-height: 1.6;
            padding-left: 12px;
            border-left: 3px solid rgba(255, 255, 255, 0.3);
          }
        }
      }

      .card-footer {
        padding: 0 25px 25px;

        .activate-btn {
          width: 100%;
          padding: 12px 20px;
          border: none;
          border-radius: 12px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
          }

          &.personal-btn {
            background: linear-gradient(135deg, #f94c30, #e63946);
            color: white;

            &:hover {
              background: linear-gradient(135deg, #e63946, #dc2626);
            }

            &.activated {
              background: var(--gray);
              color: var(--light-gray);
              cursor: not-allowed;
              opacity: 0.7;

              &:hover {
                transform: none;
                box-shadow: none;
              }
            }
          }

          &.enterprise-btn {
            background: linear-gradient(135deg, #f94c30, #e63946);
            color: white;

            &:hover {
              background: linear-gradient(135deg, #e63946, #dc2626);
            }

            &.activated {
              background: var(--gray);
              color: var(--light-gray);
              cursor: not-allowed;
              opacity: 0.7;

              &:hover {
                transform: none;
                box-shadow: none;
              }
            }
          }

          i {
            font-size: 14px;
          }
        }
      }
    }
  }

  .version-comparison-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .version-table {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(249, 76, 48, 0.05) 0%, transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4), 0 0 40px rgba(249, 76, 48, 0.1);
      border-color: rgba(249, 76, 48, 0.3);

      &::before {
        opacity: 1;
      }
    }

    .table-header {
      display: grid;
      grid-template-columns: 2fr 3fr 2fr 2fr;
      background: rgba(255, 255, 255, 0.1);
      border-bottom: 2px solid rgba(255, 255, 255, 0.15);

      > div {
        padding: 24px 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid rgba(255, 255, 255, 0.08);

        &:last-child {
          border-right: none;
        }

        h3 {
          font-size: 18px;
          font-weight: 700;
          color: var(--light);
          margin: 0;
          text-align: center;
        }
      }

      .plan-badge {
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 700;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;
        color: white;
      }
    }

    .table-body {
      .module-group {
        border-bottom: 1px solid rgba(255, 255, 255, 0.08);

        &:last-child {
          border-bottom: none;
        }
      }

      .module-row,
      .feature-row {
        display: grid;
        grid-template-columns: 2fr 3fr 2fr 2fr;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
        position: relative;

        &:last-child {
          border-bottom: none;
        }

        > div {
          padding: 16px 20px;
          display: flex;
          align-items: center;
          border-right: 1px solid rgba(255, 255, 255, 0.05);
          transition: all 0.3s ease;

          &:last-child {
            border-right: none;
          }
        }
      }

      .module-row {
        background: rgba(255, 255, 255, 0.03);

        .module-name {
          font-weight: 700;
          color: var(--light);
          font-size: 18px;
          gap: 12px;

          i {
            color: var(--primary);
            font-size: 18px;
            width: 20px;
            text-align: center;
          }
        }
      }

      .feature-name {
        font-weight: 600;
        color: var(--light);
        font-size: 16px;
      }

      .personal-feature,
      .enterprise-feature {
        justify-content: center;
        text-align: center;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        align-items: center;

        .available {
          color: #22c55e;
          padding: 8px 12px;
          border-radius: 20px;
          font-size: 16px;
          font-weight: 600;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          justify-self: center;
          grid-column: 2;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
          min-width: 40px;
          height: 40px;

          i {
            font-size: 18px;
          }

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
          }
        }

        .unavailable {
          color: #ef4444;
          padding: 8px 12px;
          border-radius: 20px;
          font-size: 16px;
          font-weight: 600;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          justify-self: center;
          grid-column: 2;
          transition: all 0.3s ease;
          opacity: 0.8;
          min-width: 40px;
          height: 40px;

          i {
            font-size: 18px;
          }
        }

        .limited {
          color: #f59e0b;
          padding: 8px 12px;
          border-radius: 20px;
          font-size: 16px;
          font-weight: 600;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          justify-self: center;
          grid-column: 2;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
          min-width: 40px;
          height: 40px;

          i {
            font-size: 18px;
            animation: limitedPulse 2s ease-in-out infinite;
          }
        }

        .feature-desc {
          font-size: 11px;
          color: var(--light-gray);
          margin-top: 4px;
          text-align: right;
          opacity: 0.8;
          font-weight: 500;
          grid-column: 3;
          justify-self: end;
        }
      }
    }
  }
}

/* 版本功能响应式设计 */
@media (max-width: 1200px) {
  .version {
    .version-summary-cards {
      max-width: 900px;
      gap: 25px;
      margin: 40px auto 50px;
    }

    .version-comparison-container {
      padding: 0 40px;
    }

    .version-table {
      .table-header,
      .module-row,
      .feature-row {
        grid-template-columns: 1.8fr 2.5fr 1.8fr 1.8fr;
      }

      .table-header > div {
        padding: 20px 16px;

        h3 {
          font-size: 16px;
        }
      }

      .plan-badge {
        padding: 10px 16px;
        font-size: 14px;
        min-width: 100px;
      }
    }
  }
}

@media (max-width: 992px) {
  .version {
    padding: 80px 0;

    .version-summary-cards {
      gap: 20px;
      margin: 30px auto 40px;
      padding: 0 20px;

      .summary-card {
        .card-header {
          padding: 20px 20px 0;

          .plan-badge {
            font-size: 13px;
            padding: 6px 12px;
          }
        }

        .card-content {
          padding: 15px 20px;

          .feature-item {
            margin-bottom: 12px;

            p {
              font-size: 13px;
            }
          }
        }

        .card-footer {
          padding: 0 20px 20px;

          .activate-btn {
            padding: 10px 16px;
            font-size: 15px;
          }
        }
      }
    }

    .version-comparison-container {
      padding: 0 20px;
    }

    .version-table {
      .table-header,
      .module-row,
      .feature-row {
        grid-template-columns: 1fr;
      }

      .table-header {
        > div {
          border-right: none;
          border-bottom: 1px solid rgba(255, 255, 255, 0.08);
          padding: 16px 20px;

          &:last-child {
            border-bottom: none;
          }
        }
      }

      .module-row,
      .feature-row {
        > div {
          border-right: none;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);
          padding: 12px 20px;

          &:last-child {
            border-bottom: none;
          }
        }
      }

      .module-row {
        .module-name {
          font-size: 16px;
          justify-content: center;
          text-align: center;
        }
      }

      .feature-name {
        font-size: 14px;
        text-align: center;
        justify-content: center;
      }

      .personal-feature,
      .enterprise-feature {
        flex-direction: column;
        gap: 8px;

        &::before {
          content: attr(data-label);
          display: block;
          font-weight: 600;
          color: var(--light-gray);
          font-size: 12px;
          text-transform: uppercase;
          order: -1;
        }

        .feature-desc {
          font-size: 10px !important;
          margin-top: 0 !important;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .version {
    padding: 60px 0;

    .version-summary-cards {
      grid-template-columns: 1fr;
      gap: 20px;
      margin: 25px auto 35px;
      padding: 0 15px;

      .summary-card {
        border-radius: 16px;

        .card-header {
          padding: 18px 18px 0;

          .plan-badge {
            font-size: 12px;
            padding: 6px 10px;

            i {
              font-size: 14px;
            }
          }

          .popular-badge {
            font-size: 11px;
            padding: 3px 10px;
          }
        }

        .card-content {
          padding: 15px 18px;

          .feature-item {
            margin-bottom: 10px;

            p {
              font-size: 12px;
            }
          }
        }

        .card-footer {
          padding: 0 18px 18px;

          .activate-btn {
            padding: 10px 16px;
            font-size: 14px;
          }
        }
      }
    }

    .version-table {
      border-radius: 12px;

      .table-header > div {
        padding: 12px 16px;

        h3 {
          font-size: 14px;
        }
      }

      .plan-badge {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 80px;
        gap: 6px;

        i {
          font-size: 12px;
        }
      }

      .module-row,
      .feature-row {
        > div {
          padding: 10px 16px;
        }
      }

      .module-name {
        font-size: 14px;
        gap: 8px;

        i {
          font-size: 16px;
        }
      }

      .feature-name {
        font-size: 13px;
      }

      .available,
      .unavailable,
      .limited {
        padding: 6px 10px !important;
        min-width: 32px !important;
        height: 32px !important;

        i {
          font-size: 14px !important;
        }
      }

      .feature-desc {
        font-size: 10px !important;
        margin-top: 2px !important;
      }
    }
  }
}

@media (max-width: 480px) {
  .version {
    .version-comparison-container {
      padding: 0 15px;
    }

    .version-table {
      .table-header > div {
        padding: 10px 12px;
      }

      .module-row,
      .feature-row {
        > div {
          padding: 8px 12px;
        }
      }

      .plan-badge {
        padding: 6px 10px;
        font-size: 11px;
        min-width: 70px;
      }

      .module-name {
        font-size: 13px;
      }

      .feature-name {
        font-size: 12px;
      }
    }
  }
}

/* 动画定义 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

  33% {
    transform: translateY(-20px) rotate(120deg);
  }

  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

@keyframes particle-float {
  0%,
  100% {
    opacity: 0;
    transform: translateY(0);
  }

  50% {
    opacity: 1;
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes dot-pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.3);
  }
}

@keyframes gentle-pulse {
  0%,
  100% {
    opacity: 0.7;
    transform: translateX(-50%) scale(1);
  }

  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.1);
  }
}

/* AI伙伴卡片新增动画 */
@keyframes float-rotate {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

  25% {
    transform: translateY(-5px) rotate(1deg) scale(1.05);
  }

  50% {
    transform: translateY(-8px) rotate(0deg) scale(1.05);
  }

  75% {
    transform: translateY(-5px) rotate(-1deg) scale(1.05);
  }
}

@keyframes glow-pulse {
  0%,
  100% {
    opacity: 0.6;
    filter: blur(8px);
  }

  50% {
    opacity: 1;
    filter: blur(12px);
  }
}

@keyframes text-glow {
  0% {
    text-shadow: 0 0 10px rgba(var(--primary-rgb), 0.6);
  }

  100% {
    text-shadow: 0 0 10px rgba(var(--primary-rgb), 0.8), 0 0 20px rgba(var(--primary-rgb), 0.4),
      0 0 30px rgba(var(--primary-rgb), 0.2);
  }
}

@keyframes sweep-light {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }

  50% {
    transform: translateX(0%) rotate(45deg);
  }

  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* 响应式设计 */
@media (max-width: 1000px) {
  .ai-assistants {
    .yan-introduction {
      grid-template-columns: 1fr;
      gap: 40px;
    }

    .capability-card {
      padding: 25px;
    }

    .character-image {
      width: 280px;
      height: 360px;
    }
  }
}

@media (max-width: 992px) {
  .ai-assistants {
    padding: 80px 0 0 0;

    .section-title {
      margin-bottom: 60px;

      .title-wrapper h2 {
        font-size: 28px;
      }

      .title-wrapper p {
        font-size: 16px;
        padding: 12px 25px;
      }
    }

    .yan-introduction {
      grid-template-columns: 1fr;
      gap: 40px;
    }

    .character-image {
      width: 280px;
      height: 360px;

      .chat-bubble {
        left: -110px;
      }
    }
  }
}

@media (max-width: 768px) {
  .ai-assistants {
    padding: 60px 0 0 0;

    .section-title {
      .title-wrapper {
        gap: 15px;

        .title-line {
          width: 40px;
        }

        h2 {
          font-size: 24px;
        }

        p {
          font-size: 14px;
          padding: 10px 20px;
          max-width: 90%;
        }
      }
    }

    .capability-card {
      padding: 20px;

      .card-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;

        i {
          font-size: 20px;
        }
      }

      h3 {
        font-size: 18px;
      }

      p {
        font-size: 14px;
      }
    }

    .character-image {
      width: 250px;
      height: 320px;

      .chat-bubble {
        left: -100px;
        top: 30px;
      }
    }
  }
}

/* Responsive AI Assistants */
@media (max-width: 1200px) {
  .ai-assistants .ai-assistant-grid {
    gap: 30px;
  }

  .ai-assistants .ai-assistant-card {
    padding: 30px 25px;
  }
}

@media (max-width: 992px) {
  .ai-assistants .ai-assistant-grid {
    grid-template-columns: 1fr; // Stack on tablets
    gap: 30px;
  }

  .ai-assistants .ai-assistant-card {
    max-width: 500px; // Limit width when stacked
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 768px) {
  .ai-assistants {
    padding: 80px 0;
  }

  .ai-assistants .ai-assistant-card {
    padding: 25px 20px;

    .ai-assistant-icon-wrapper .ai-assistant-icon {
      width: 70px;
      height: 70px;

      i {
        font-size: 28px;
      }
    }

    .ai-assistant-name {
      font-size: 20px;
    }

    .ai-assistant-role {
      font-size: 14px;
    }

    .ai-assistant-desc {
      font-size: 13px;
    }
  }
}

/* 核心环节 */
.core-sections {
  padding: 100px 0;
  background-color: var(--secondary);

  .core-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 60px;
  }

  .core-card {
    background-color: var(--gray);
    border-radius: 10px;
    padding: 50px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-align: center;
    transition: all 0.3s ease-in-out;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 4px solid transparent;

    &:hover {
      transform: translateY(-10px) perspective(1000px) rotateX(-1deg) rotateY(1deg);
      border-bottom: 4px solid var(--primary);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.35);
      background-color: var(--gray); // Keep background or specify change

      .core-icon {
        transform: scale(1.1) translateY(-5px);
      }

      .btn-outline {
        background-color: var(--primary);
        border-color: var(--primary);
        color: var(--light);
      }
    }

    .core-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
      margin-bottom: 24px;
      box-shadow: 0 6px 12px rgba(249, 76, 48, 0.3);
      transition: transform 0.3s ease-in-out;
      font-size: 24px;
      color: var(--light);
    }

    .core-title {
      font-size: 24px;
      color: var(--light);
      margin-bottom: 16px;
    }

    .core-desc {
      font-size: 16px;
      color: var(--light-gray);
      line-height: 1.6;
      margin-bottom: 24px;
    }

    .btn-outline {
      border-color: var(--light-gray);
      color: var(--light-gray);
    }
  }
}

/* 注册区 */
.register {
  padding: 100px 0;
  background-color: var(--dark);
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  &:hover .register-image img {
    transform: scale(1.03);
  }

  .register-container {
    display: flex;
    gap: 60px;
    align-items: center;
  }

  .register-image {
    flex: 1;

    img {
      max-width: 100%;
      border-radius: 10px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: transform 0.5s ease-out;
    }
  }

  .register-content {
    flex: 1;
  }

  .register-title {
    font-size: 36px;
    color: var(--light);
    margin-bottom: 24px;
  }

  .register-desc {
    font-size: 18px;
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 40px;
  }

  .register-points {
    margin-bottom: 40px;
  }

  .register-point {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    i {
      color: var(--primary);
      font-size: 24px;
      margin-right: 16px;
      min-width: 24px;
    }

    p {
      font-size: 18px;
      color: var(--light);
    }
  }

  .register-buttons {
    display: flex;
    gap: 20px;

    .btn-outline {
      color: var(--light);
      border-color: var(--light);

      &:hover {
        background-color: var(--light);
        color: var(--primary);
        border-color: var(--light);
      }
    }
  }
}

/* 合作伙伴 */
.partners {
  padding: 80px 0;
  background-color: var(--secondary);
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .section-title {
    h2 {
      color: var(--light);
    }

    p {
      color: var(--light-gray);
    }
  }

  .partners-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 60px;
    margin-top: 40px;
    filter: grayscale(80%) brightness(1.5);
    opacity: 0.7;
    transition: all 0.3s;

    &:hover {
      filter: grayscale(0) brightness(1);
      opacity: 1;
    }
  }

  .partner-item {
    max-width: 140px;
  }
}

/* CTA区 */
.cta {
  padding: 120px 0;
  background-color: var(--secondary);
  color: var(--light);
  position: relative;
  overflow: hidden;

  .cta-content {
    position: relative;
    z-index: 10;
  }

  .cta-header {
    text-align: center;
    margin-bottom: 60px;

    .cta-title {
      font-size: 36px;
      margin-bottom: 24px;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
      font-weight: 700;
      color: var(--light);
    }

    .cta-subtitle {
      font-size: 20px;
      max-width: 700px;
      margin: 0 auto;
      opacity: 0.9;
      line-height: 1.6;
    }
  }

  .comparison-container {
    margin-bottom: 80px;

    .comparison-table {
      background: rgba(255, 255, 255, 0.08);
      border-radius: 20px;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(15px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      max-width: 900px;
      margin: 0 auto;

      .table-header {
        display: grid;
        grid-template-columns: 1fr 2fr 2fr;
        background: rgba(255, 255, 255, 0.1);

        > div {
          padding: 24px 20px;
          display: flex;
          align-items: center;
          justify-content: center;

          &.feature-column {
            justify-content: center;
            font-weight: 700;
            color: var(--light);
            font-size: 20px;
          }
        }

        .plan-badge {
          padding: 12px 20px;
          border-radius: 25px;
          font-weight: 700;
          font-size: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 120px;
          justify-content: center;

          &.personal {
            background: rgba(156, 163, 175, 0.3);
            color: #e5e7eb;
            border: 2px solid rgba(156, 163, 175, 0.5);
          }

          &.enterprise {
            background: linear-gradient(135deg, #ff4c30 0%, #ff6b50 100%);
            color: var(--light);
            position: relative;
            overflow: hidden;
            border: 2px solid #ff4c30;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
              animation: shimmer 2s infinite;
            }

            i {
              font-size: 14px;
            }
          }
        }
      }

      .table-body {
        .feature-row {
          display: grid;
          grid-template-columns: 1fr 2fr 2fr;
          border-bottom: 1px solid rgba(255, 255, 255, 0.08);

          &:last-child {
            border-bottom: none;
          }

          > div {
            padding: 16px 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
          }

          .feature-name {
            align-items: flex-start;
            font-weight: 600;
            color: var(--light);
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 12px;

            i {
              color: #ff4c30;
              font-size: 18px;
              width: 20px;
            }
          }

          .personal-feature,
          .enterprise-feature {
            align-items: center;
            text-align: center;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            align-items: center;

            .available,
            .unavailable,
            .limited {
              justify-self: center;
              grid-column: 2;
            }

            .feature-desc {
              font-size: 13px;
              color: rgba(255, 255, 255, 0.7);
              margin-top: 6px;
              line-height: 1.4;
              grid-column: 3;
              justify-self: end;
              text-align: right;
            }
          }

          .limited,
          .basic {
            color: #ffc107;
          }

          .unlimited,
          .available,
          .premium {
            color: #22c55e;
          }

          .unavailable {
            color: #ef4444;
          }

          .limited,
          .unlimited,
          .available,
          .unavailable,
          .basic,
          .premium {
            padding: 8px 16px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
          }
        }
      }
    }
  }

  .cta-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    max-width: 800px;
    margin: 0 auto;

    .action-card {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.15);
      border-radius: 24px;
      padding: 40px 32px;
      text-align: center;
      position: relative;
      transition: all 0.3s ease;
      backdrop-filter: blur(15px);

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }

      &.enterprise-card {
        border-color: #ff4c30;
        background: linear-gradient(135deg, rgba(255, 76, 48, 0.12) 0%, rgba(255, 107, 80, 0.08) 100%);

        &:hover {
          border-color: #ff6b50;
          box-shadow: 0 20px 40px rgba(255, 76, 48, 0.25);
          background: linear-gradient(135deg, rgba(255, 76, 48, 0.15) 0%, rgba(255, 107, 80, 0.1) 100%);
        }

        .recommended-badge {
          position: absolute;
          top: -12px;
          right: 20px;
          background: linear-gradient(135deg, #ff4c30 0%, #ff6b50 100%);
          color: var(--light);
          padding: 6px 16px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: 700;
          display: flex;
          align-items: center;
          gap: 4px;
          box-shadow: 0 4px 12px rgba(255, 76, 48, 0.4);

          i {
            font-size: 10px;
          }
        }
      }

      h3 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 12px;
        color: var(--light);
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 24px;
        line-height: 1.5;
      }

      .price {
        margin-bottom: 32px;
        display: flex;
        align-items: baseline;
        justify-content: center;
        gap: 4px;

        .currency {
          font-size: 24px;
          font-weight: 600;
          color: var(--light);
        }

        .amount {
          font-size: 48px;
          font-weight: 900;
          color: var(--light);
        }

        .period {
          font-size: 18px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.7);
        }
      }

      // 限时免费促销样式
      .limited-time-promotion {
        background: linear-gradient(135deg, rgba(255, 69, 0, 0.15) 0%, rgba(255, 140, 0, 0.1) 100%);
        border: 2px solid rgba(255, 69, 0, 0.3);
        border-radius: 16px;
        padding: 16px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
        animation: promotionPulse 2s ease-in-out infinite;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
          animation: goldShimmer 3s infinite;
        }

        .promotion-tag {
          background: linear-gradient(135deg, #ff4500 0%, #ff8c00 100%);
          color: white;
          padding: 6px 12px;
          border-radius: 16px;
          font-weight: 600;
          font-size: 14px;
          display: inline-flex;
          align-items: center;
          gap: 6px;
          box-shadow: 0 2px 8px rgba(255, 69, 0, 0.3);
          transform: rotate(-3deg);
          margin-bottom: 12px;

          i {
            font-size: 12px;
            animation: fireFlicker 0.5s ease-in-out infinite alternate;
          }
        }

        .countdown-timer {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          flex-wrap: wrap;

          .timer-item {
            background: rgba(0, 0, 0, 0.6);
            border-radius: 6px;
            padding: 6px 8px;
            text-align: center;
            min-width: 40px;

            .timer-number {
              display: block;
              font-size: 16px;
              font-weight: 700;
              color: #ffd700;
              line-height: 1;
            }

            .timer-label {
              display: block;
              font-size: 10px;
              color: var(--light-gray);
              margin-top: 2px;
            }
          }

          .timer-separator {
            font-size: 14px;
            font-weight: 700;
            color: #ffd700;
            animation: separatorBlink 1s ease-in-out infinite;
          }
        }
      }

      // 价格对比展示样式
      .pricing-display {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40px;
        margin-bottom: 24px;
        flex-wrap: wrap;

        .original-price {
          position: relative;
          text-align: center;
          opacity: 0.7;

          .price-label {
            display: block;
            font-size: 12px;
            color: var(--light-gray);
            margin-bottom: 4px;
          }

          .price-value {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 2px;

            .currency {
              font-size: 16px;
              font-weight: 600;
              color: var(--light);
            }

            .amount {
              font-size: 24px;
              font-weight: 700;
              color: var(--light);
            }

            .period {
              font-size: 12px;
              font-weight: 500;
              color: rgba(255, 255, 255, 0.7);
            }
          }

          .strike-through {
            position: absolute;
            top: 55%;
            left: 0;
            right: 0;
            height: 3px;
            background: #ff4444;
            transform: translateY(-50%) rotate(15deg);
            border-radius: 2px;
          }
        }

        .current-price {
          text-align: center;
          position: relative;

          .price-label {
            display: block;
            font-size: 14px;
            color: #ffd700;
            margin-bottom: 4px;
            font-weight: 600;
          }

          .price-value {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 2px;

            .currency {
              font-size: 20px;
              font-weight: 600;
              background: linear-gradient(45deg, #ffd700, #ffed4e);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            .amount {
              font-size: 36px;
              font-weight: 900;
              background: linear-gradient(45deg, #ffd700, #ffed4e);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
              animation: priceGlow 2s ease-in-out infinite;
            }
          }

          .free-badge {
            position: absolute;
            top: -8px;
            right: -16px;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 3px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 700;
            transform: rotate(15deg);
            box-shadow: 0 2px 6px rgba(76, 175, 80, 0.4);
          }
        }
      }

      .btn {
        width: 100%;
        padding: 16px 24px;
        border-radius: 25px;
        font-weight: 700;
        font-size: 16px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: center;

        &.btn-outline {
          background: transparent;
          color: var(--light);
          border: 2px solid rgba(255, 255, 255, 0.3);

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
          }
        }

        &.btn-primary {
          background: var(--primary);
          color: var(--light);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 76, 48, 0.4);

            &::before {
              left: 100%;
            }
          }

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}

.btn-light {
  background-color: var(--light);
  color: var(--primary);
  border-color: var(--light);

  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: scale(1.05);
    color: #d63a1f;
  }
}

.btn-outline-light {
  background-color: transparent;
  color: var(--light);
  border: 1px solid var(--light);

  &:hover {
    background-color: var(--light);
    color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
  }

  &:hover::before {
    // Specific override for this button
    display: none;
  }
}

/* 简单动画 Keyframes */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes promotionPulse {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(255, 69, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 69, 0, 0.5), 0 0 40px rgba(255, 140, 0, 0.3);
  }
}

@keyframes goldShimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes fireFlicker {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes separatorBlink {
  0%,
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}

@keyframes priceGlow {
  0%,
  100% {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    transform: scale(1);
  }
  50% {
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.4);
    transform: scale(1.02);
  }
}

/* 新增图标动画 */
@keyframes limitedPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes checkPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    filter: brightness(1.3);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes errorShake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-3px);
  }
  75% {
    transform: translateX(3px);
  }
}

@keyframes warningBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-3px);
  }
  50% {
    transform: translateY(-5px);
  }
  75% {
    transform: translateY(-2px);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    padding: 0 40px;
    /* Reduce padding for smaller desktops */
  }

  .hero .hero-title {
    font-size: 44px;
  }

  .features .feature-grid,
  .core-sections .core-grid,
  .ai-assistants .ai-partners-container {
    grid-template-columns: repeat(2, 1fr);
    /* Two columns for tablets/small desktops */
  }
}

@media (max-width: 992px) {
  header .navbar {
    padding: 15px 0;
  }

  header .logo {
    width: 160px;
    /* Smaller logo */
  }

  header .nav-links {
    display: none;
    /* Hide desktop links */
  }

  header .nav-buttons {
    gap: 10px;

    /* Hide desktop buttons if mobile menu is open */
    /* This selector might need adjustment depending on final structure */
    .mobile-menu.is-open ~ .container & {
      // Check if this selector works correctly
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s;
    }
  }

  /* Show hamburger only on smaller screens */
  .mobile-nav-toggle {
    display: block;
  }

  .hero {
    padding-top: 73px;
    text-align: center;
  }

  .hero .hero-content {
    flex-direction: column;
    /* Stack hero content */
    text-align: center;
  }

  .hero .hero-text {
    padding-right: 0;
    margin-bottom: 40px;
  }

  .hero .hero-title {
    font-size: 38px;
  }

  .hero .hero-subtitle {
    font-size: 18px;
  }

  .section-title h2 {
    font-size: 30px;
  }

  .section-title p {
    font-size: 16px;
  }

  .features {
    padding: 80px 0;
  }

  .core-sections {
    padding: 80px 0;
  }

  .register {
    padding: 80px 0;
  }

  .partners {
    padding: 60px 0;
  }

  .cta {
    padding: 80px 0;
  }

  footer {
    padding: 60px 0 20px;
  }

  .features .feature-grid,
  .core-sections .core-grid,
  .ai-assistants .ai-partners-container {
    grid-template-columns: 1fr;
    /* Single column for smaller devices */
    gap: 30px;
  }

  .register .register-container {
    flex-direction: column;
    /* Stack register content */
  }

  .register .register-image {
    margin-bottom: 40px;
    text-align: center;
    /* Center image if needed */
  }

  .register .register-content {
    text-align: center;
    /* Center text */
  }

  .register .register-points {
    display: inline-block;
    /* Center points container */
    text-align: left;
    /* Align text left within container */
  }

  .register .register-buttons {
    justify-content: center;
  }

  .cta {
    padding: 80px 0;

    .cta-header .cta-title {
      font-size: 36px;
    }

    .cta-header .cta-subtitle {
      font-size: 18px;
    }

    .comparison-container {
      margin-bottom: 60px;

      .comparison-table {
        .table-header,
        .feature-row {
          grid-template-columns: 1.5fr 1fr 1fr;

          > div {
            padding: 20px 16px;
          }

          .feature-name {
            font-size: 15px;

            i {
              margin-right: 8px;
            }
          }
        }

        .plan-badge {
          padding: 10px 16px;
          font-size: 14px;
        }
      }
    }

    .cta-actions {
      gap: 30px;

      .action-card {
        padding: 32px 24px;

        h3 {
          font-size: 24px;
        }

        .price .amount {
          font-size: 40px;
        }
      }
    }
  }

  footer .footer-content {
    grid-template-columns: 1fr;
    /* Stack footer columns */
    text-align: center;
  }

  footer .footer-info {
    max-width: none;
    /* Remove max-width */
    margin-bottom: 40px;
  }

  footer .footer-logo {
    margin-left: auto;
    margin-right: auto;
  }

  footer .footer-social {
    justify-content: center;
    /* Center social icons */
  }

  footer .footer-links {
    margin-bottom: 30px;
  }

  footer .footer-links ul {
    padding-left: 0;
    /* Remove default padding */
  }

  /* END OF REMOVALS FROM 992px media query */
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
    /* Further reduce padding for mobile */
  }

  header .nav-buttons .btn {
    padding: 8px 16px;
    /* Smaller buttons */
    font-size: 14px;
  }

  .hero {
    padding-top: 68px;
    min-height: 80vh;
  }

  .hero .hero-image {
    display: none;
    /* Hide hero image on mobile */
  }

  .hero .hero-title {
    font-size: 32px;
  }

  .hero .hero-subtitle {
    font-size: 16px;
  }

  .hero .hero-buttons {
    flex-direction: column;
    align-items: center;
    /* Center buttons horizontally when stacked */
    gap: 15px;
    /* Add gap like CTA buttons */
  }

  .hero .hero-buttons .btn {
    padding: 10px 50px;
    font-size: 15px;
    width: 80%;
    /* Match CTA button width */
    max-width: 250px;
    /* Match CTA button width */
  }

  .hero .hero-buttons .enterprise-link {
    font-size: 14px;
    padding: 10px 0;
  }

  .features .feature-card,
  .core-sections .core-card {
    padding: 30px 20px;
  }

  .features .feature-title,
  .core-sections .core-title {
    font-size: 20px;
  }

  .features .feature-desc,
  .core-sections .core-desc,
  .register .register-desc {
    font-size: 15px;
  }

  .register .register-title {
    font-size: 30px;
  }

  .register .register-point p {
    font-size: 16px;
  }

  .partners .partners-logo {
    gap: 30px;
  }

  .partners .partner-item {
    max-width: 100px;
    /* Smaller partner logos */
  }

  .cta {
    padding: 60px 0;

    .cta-header .cta-title {
      font-size: 28px;
    }

    .cta-header .cta-subtitle {
      font-size: 16px;
    }

    .comparison-container {
      margin-bottom: 40px;

      .comparison-table {
        .table-header,
        .feature-row {
          grid-template-columns: 1fr;

          .personal-column,
          .enterprise-column,
          .personal-feature,
          .enterprise-feature {
            border-top: 1px solid rgba(255, 255, 255, 0.08);
          }

          .feature-name {
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            padding-bottom: 16px;
            margin-bottom: 16px;
            font-size: 14px;
          }

          > div {
            padding: 16px 20px;
          }
        }

        .plan-badge {
          padding: 8px 14px;
          font-size: 13px;
        }
      }
    }

    .cta-actions {
      grid-template-columns: 1fr;
      gap: 24px;
      max-width: 400px;

      .action-card {
        padding: 28px 24px;

        h3 {
          font-size: 22px;
        }

        p {
          font-size: 15px;
        }

        .price {
          .currency {
            font-size: 20px;
          }

          .amount {
            font-size: 36px;
          }

          .period {
            font-size: 16px;
          }
        }

        .limited-time-promotion {
          padding: 12px;
          margin-bottom: 16px;

          .promotion-tag {
            font-size: 12px;
            padding: 4px 8px;
            margin-bottom: 8px;
          }

          .countdown-timer {
            gap: 4px;

            .timer-item {
              min-width: 35px;
              padding: 4px 6px;

              .timer-number {
                font-size: 14px;
              }

              .timer-label {
                font-size: 9px;
              }
            }

            .timer-separator {
              font-size: 12px;
            }
          }
        }

        .pricing-display {
          gap: 20px;
          margin-bottom: 20px;

          .original-price {
            .price-label {
              font-size: 10px;
            }

            .price-value {
              .currency {
                font-size: 14px;
              }

              .amount {
                font-size: 20px;
              }

              .period {
                font-size: 10px;
              }
            }
          }

          .current-price {
            .price-label {
              font-size: 12px;
            }

            .price-value {
              .currency {
                font-size: 16px;
              }

              .amount {
                font-size: 28px;
              }
            }

            .free-badge {
              font-size: 8px;
              padding: 2px 6px;
              top: -6px;
              right: -12px;
            }
          }
        }

        .btn {
          padding: 14px 20px;
          font-size: 15px;
        }
      }
    }
  }

  .cta .cta-buttons .btn {
    width: 80%;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
    /* Reduce side padding on very small screens */
  }

  header .logo {
    width: 130px;
  }

  header .nav-buttons .btn:first-child {
    display: none;
    /* Hide Login on very small screens maybe? */
  }

  .hero .hero-title {
    font-size: 28px;
  }

  .hero .hero-subtitle {
    font-size: 14px;
  }

  .section-title h2 {
    font-size: 26px;
  }

  .section-title p {
    font-size: 14px;
  }

  footer .footer-logo {
    font-size: 20px;
  }

  footer .footer-desc,
  footer .footer-links a {
    font-size: 14px;
  }

  footer .copyright p {
    font-size: 12px;
  }
}

/* Mobile Nav Toggle Button */
.mobile-nav-toggle {
  display: none;
  /* Hidden by default, shown in media query */
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  z-index: 1001;
  /* Above mobile menu */
  position: relative;
  /* Needed for z-index */
}

.hamburger-icon {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--light);
  position: relative;
  transition: background-color 0.3s ease-in-out;
}

.hamburger-icon::before,
.hamburger-icon::after {
  content: '';
  position: absolute;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--light);
  transition: transform 0.3s ease-in-out, top 0.3s ease-in-out;
}

.hamburger-icon::before {
  top: -7px;
}

.hamburger-icon::after {
  top: 7px;
}

/* Mobile Menu Styles */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  /* Changed from right */
  width: 100%;
  height: 100%;
  background-color: rgba(30, 30, 38, 0.98);
  /* Dark background */
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: translateX(-100%);
  /* Start off-screen to the left */
  transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  /* Smooth slide */
  padding-top: 80px;
  /* Space for potential header overlap */
  box-sizing: border-box;
  /* Include padding in height/width */
  overflow-x: hidden;
}

.mobile-menu.is-open {
  transform: translateX(0);
}

/* Style for the close (X) state of the hamburger when menu is open */
.mobile-menu.is-open~header.scrolled .mobile-nav-toggle .hamburger-icon,
/* Check scrolled state */
.mobile-menu.is-open~header:not(.scrolled) .mobile-nav-toggle .hamburger-icon {
  /* Check non-scrolled state */
  background-color: transparent;
  /* Middle line disappears */
}

.mobile-menu.is-open ~ header.scrolled .mobile-nav-toggle .hamburger-icon::before,
.mobile-menu.is-open ~ header:not(.scrolled) .mobile-nav-toggle .hamburger-icon::before {
  transform: rotate(45deg);
  top: 0;
}

.mobile-menu.is-open ~ header.scrolled .mobile-nav-toggle .hamburger-icon::after,
.mobile-menu.is-open ~ header:not(.scrolled) .mobile-nav-toggle .hamburger-icon::after {
  transform: rotate(-45deg);
  top: 0;
}

.mobile-menu-links {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  margin-bottom: 40px;
}

.mobile-menu-links a {
  color: var(--light);
  font-size: 22px;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.mobile-menu-links a:hover {
  color: var(--primary);
}

.mobile-menu-buttons {
  display: flex;
  flex-direction: column;
  /* Stack buttons */
  align-items: center;
  gap: 20px;
  width: 80%;
  max-width: 300px;
  /* Limit button width */
}

.mobile-menu-buttons .btn {
  width: 100%;
  text-align: center;
  padding: 12px 24px;
}

/* 插件区 */
.plugin {
  padding: 100px 0;
  background-color: var(--secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .plugin-container {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 60px;
  }

  .plugin-image {
    flex: 1;
    max-width: 50%;
    margin-right: 100px;

    img {
      width: 100%;
      border-radius: 10px;
    }
  }

  .plugin-content {
    flex: 1;
  }

  .plugin-title {
    font-size: 32px;
    color: var(--light);
    margin-bottom: 24px;
  }

  .plugin-desc {
    font-size: 17px;
    color: var(--light-gray);
    line-height: 1.7;
    margin-bottom: 40px;
  }

  .plugin-features {
    margin-bottom: 40px;
  }

  .plugin-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;

    i {
      color: var(--primary);
      font-size: 24px;
      margin-right: 20px;
      min-width: 24px;
    }

    h4 {
      font-size: 20px;
      color: var(--light);
      margin-bottom: 8px;
    }

    p {
      font-size: 16px;
      color: var(--light-gray);
      line-height: 1.5;
    }
  }

  .plugin-buttons {
    display: flex;
    gap: 20px;
  }
}

/* 用户菜单样式 */
.user-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-icon {
  position: relative;
  cursor: pointer;

  i {
    font-size: 20px;
    color: var(--light);
    transition: color 0.3s;
  }

  &:hover i {
    color: var(--primary);
  }

  .notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}

.user-dropdown {
  position: relative;

  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .user-name-container {
      display: flex;
      flex-direction: column;

      .username {
        color: var(--light);
        font-weight: 500;
      }

      .company-name {
        color: var(--light-gray);
        line-height: 1.2;
      }
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid var(--primary);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    i {
      color: var(--light-gray);
      font-size: 12px;
      transition: transform 0.3s;
    }
  }

  &:hover .user-info i {
    color: var(--primary);
  }

  .dropdown-menu.show ~ .user-info i,
  .user-info:hover i {
    transform: rotate(180deg);
  }

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background-color: var(--secondary);
    border-radius: 8px;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transform: translateY(-10px);
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1001;

    &.show {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }

    &::before {
      content: '';
      position: absolute;
      top: -6px;
      right: 20px;
      width: 12px;
      height: 12px;
      background-color: var(--secondary);
      transform: rotate(45deg);
      border-left: 1px solid rgba(255, 255, 255, 0.1);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px 16px;
      color: var(--light);
      text-decoration: none;
      transition: background-color 0.3s;

      i {
        width: 16px;
        color: var(--light-gray);
      }

      &:hover {
        background-color: rgba(249, 76, 48, 0.1);

        i {
          color: var(--primary);
        }
      }

      &:first-child {
        border-radius: 8px 8px 0 0;
      }

      &:last-child {
        border-radius: 0 0 8px 8px;
      }
    }

    .dropdown-divider {
      height: 1px;
      background-color: rgba(255, 255, 255, 0.1);
      margin: 6px 0;
    }
  }
}

/* 响应式用户菜单 */
@media (max-width: 992px) {
  .user-menu {
    .user-name-container {
      display: none;
    }

    .user-info i {
      display: none;
    }
  }

  /* 移动端联系方式布局改为垂直排列 */
  footer .footer-links:last-child > div {
    flex-direction: column;
    align-items: center;

    ul {
      width: 100%;
      text-align: center;
    }

    div {
      margin-top: 10px;
      width: 100%;
      text-align: center;
    }
  }
}

@media (max-width: 768px) {
  .notification-icon {
    i {
      font-size: 18px;
    }

    .notification-badge {
      width: 16px;
      height: 16px;
      font-size: 10px;
    }
  }

  .user-dropdown .avatar {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .user-menu {
    gap: 12px;
  }
}

/* Responsive styles for plugin section */
@media (max-width: 1200px) {
  .plugin .plugin-title {
    font-size: 28px;
  }
}

@media (max-width: 992px) {
  .plugin {
    padding: 80px 0;
  }

  .plugin .plugin-container {
    flex-direction: column;
    gap: 40px;
  }

  .plugin .plugin-image {
    max-width: 100%;
  }

  .plugin .plugin-content {
    text-align: center;
  }

  .plugin .plugin-feature {
    text-align: left;
  }

  .plugin .plugin-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .plugin .plugin-title {
    font-size: 26px;
  }

  .plugin .plugin-desc {
    font-size: 16px;
  }

  .plugin .plugin-feature h4 {
    font-size: 18px;
  }

  .plugin .plugin-feature p {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .plugin .plugin-title {
    font-size: 24px;
  }

  .plugin .plugin-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .plugin .plugin-buttons .btn {
    width: 80%;
    max-width: 250px;
  }
}

/* 回到顶部按钮样式 */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 50%, #f94c30 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(249, 76, 48, 0.3);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(249, 76, 48, 0.5);
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 50%, #a93226 100%);
  }

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  i {
    color: white;
    font-size: 20px;
    transition: transform 0.2s ease;
  }

  &:hover i {
    transform: translateY(-2px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;

    i {
      font-size: 18px;
    }
  }
}
</style>
