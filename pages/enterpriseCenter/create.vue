<template>
  <NuxtLayout name="child-page" title="创建企业">
    <div w-full max-w-150>
      <a-form size="large" ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 24 }">
        <a-form-item label="企业名称" name="merchantName">
          <Bussiness
            v-model:value="formState.merchantName"
            @change="handleBussinessChange"
            placeholder="请输入企业名称"
          />
        </a-form-item>
        <a-form-item label="联系电话" name="merchantPhone">
          <a-input v-model:value="formState.merchantPhone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item label="邮箱" name="merchantMail">
          <a-input v-model:value="formState.merchantMail" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item label="主营" name="mainProduct">
          <a-textarea v-model:value="formState.mainProduct" placeholder="请输入主营业务" :rows="3" />
        </a-form-item>
      </a-form>
      <div my-4>
        <a-checkbox v-model:checked="checked">
          我已阅读并同意
          <span button-link @click="onOpenRegistration">《研选工场企业入驻协议》</span>
        </a-checkbox>
      </div>
      <div flex items-center justify-center>
        <a-button flex-1 type="primary" size="large" :disabled="!checked" @click="onSubmitClick">完成</a-button>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import { saveMember } from '~/api/mall-manage/index'

const router = useRouter()
const formRef = ref(null)

const formState = reactive({
  merchantName: undefined,
  merchantPhone: undefined,
  merchantMail: undefined,
  mainProduct: undefined,
  creditCode: undefined,
})

const rules = {
  merchantName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  merchantPhone: [
    { pattern: /^(?:(?:\+|00)86)?1\d{2}([\d*]{4})\d{4}$/, message: '请输入正确的联系方式', trigger: 'blur' },
  ],
  merchantMail: [
    {
      pattern: /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/,
      message: '请输入正确的邮箱地址',
      trigger: ['blur', 'change'],
    },
  ],
}

const checked = ref(false)

/**
 * @typedef {0 | 1 | 2} JoinStatus 0 企业审核中, 1 可以加入
 */

/**
 * @returns {Promise<{ existed: boolean, type: number, joinStatus: JoinStatus } | null>}
 */
const checkIsJoin = async () => {
  const [err, res] = await try_http('/mall/p/shopCompany/check', {
    params: {
      creditCode: formState.creditCode,
      merchantId: 0,
    },
  })
  if (err) return Promise.reject()
  if (res.data) {
    const { type, shopCompanyId, status } = res.data
    const result = {
      existed: true,
      shopCompanyId,
      type,
    }
    let joinStatus
    if (status == 2) {
      joinStatus = 0
    } else if (eq(joinStatus, 10, 20)) {
      return null
    } else {
      joinStatus = 1
    }
    return result
  }
}

/**
 * @param {{ creditCode: string, name: string }}
 */
const handleBussinessChange = ({ creditCode }) => {
  console.log('%c Line:77 🥛 creditCode', 'color:#599458', creditCode)
  formState.creditCode = creditCode
}

const { user } = storeToRefs(userStore())

const onSubmitClick = async () => {
  try {
    await formRef.value.validate()
    const res = await checkIsJoin()
    if (res) {
      const { existed, type, joinStatus, shopCompanyId } = res
      if (existed) {
        if (joinStatus == 0) {
          message.info('当前企业正在审核中')
          return
        }
        if (eq(type, 1, 3)) {
          return Modal.confirm({
            title: '提示',
            content: '该企业已入驻研选工场，是否申请加入？',
            okText: '是',
            cancelText: '否',
            async onOk() {
              const [err] = await try_http('/mall-platform/shop/merchantUser', {
                method: 'post',
                body: {
                  merchantId: shopCompanyId,
                  userMobile: user.value.userMobile,
                  shopId: 0,
                  auditStatus: 0,
                  auditResult: 0,
                },
              })
              if (err) return
              message.success('申请已提交,请耐心等待审核')
              navigateTo('/userCenter')
            },
          })
        } else if (eq(type, 2)) {
          message.info('该企业已入驻研选工场，无法重复创建')
          return
        }
      }
    }
    onSubmit()
  } catch (error) {
    // 验证失败，不做处理
  }
}

const onOpenRegistration = () => {
  const currentDomain = window.location.origin // 获取当前域名
  const newPath = '/agreement?key=registration' // 新的路径
  const newLink = `${currentDomain}${newPath}` // 拼接新的完整链接
  window.open(newLink, '_blank')
}

const onSubmit = () => {
  saveMember({
    type: 1,
    ...formState,
  }).then((res) => {
    if (res.code === '00000') {
      message.success('申请已提交,请耐心等待审核')
      router.push('/userCenter')
    }
  })
}
</script>

<style scoped></style>
